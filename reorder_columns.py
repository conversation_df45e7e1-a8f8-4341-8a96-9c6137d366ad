import csv

# 读取原始文件
with open('taiwan_dams_complete.csv', 'r', encoding='utf-8-sig') as f:
    reader = csv.reader(f, delimiter='\t')
    rows = list(reader)

# 原始列名
original_headers = rows[0]
print('原始列名:', original_headers)

# 用户要求的新列顺序
new_headers = [
    '目标名称（简体中文）',
    '目标名称（繁体中文）', 
    '目标名称（英文）',
    '目标类别',
    '坐标（经度）',
    '坐标（纬度）',
    '基本情况',
    '卫星图（如果有）',
    '备注',
    '目标性质',
    '重要性'
]

# 创建列名映射
header_mapping = {
    '目标名称（简体中文）': '目标名称（简体中文）',
    '目标名称（繁体中文）': '目标名称（繁体中文）',
    '目标名称（英文）': '目标名称（英文）',
    '目标类别': '目标类别',
    '目标性质': '目标性质',
    '重要性（1-5级）': '重要性',
    '坐标（经度）': '坐标（经度）',
    '坐标（纬度）': '坐标（纬度）',
    '基本情况': '基本情况',
    '卫星图（如果有）': '卫星图（如果有）',
    '备注': '备注'
}

# 创建列索引映射
column_indices = {}
for i, header in enumerate(original_headers):
    if header in header_mapping:
        column_indices[header_mapping[header]] = i

print('列索引映射:', column_indices)

# 创建新文件
with open('taiwan_dams_reordered.csv', 'w', encoding='utf-8-sig', newline='') as f:
    writer = csv.writer(f, delimiter='\t')
    
    # 写入新标题
    writer.writerow(new_headers)
    
    # 处理每一行数据
    for row in rows[1:]:
        new_row = []
        for header in new_headers:
            if header in column_indices:
                original_index = column_indices[header]
                if original_index < len(row):
                    new_row.append(row[original_index])
                else:
                    new_row.append('')
            else:
                new_row.append('')
        writer.writerow(new_row)

print('文件重新排序完成！')